"use client";

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useGetTaxRatesQuery } from '@/redux/services/taxRatesApi';
import {
  Calculator,
  PercentSquare,
  FileText,
  ArrowRight,
  BarChart4,
  Receipt,
  Loader2
} from "lucide-react";

export default function TaxesPage() {
  const [activeTab, setActiveTab] = useState("overview");

  // Fetch tax rates data
  const { data: taxRates, isLoading: isLoadingTaxRates, error: taxRatesError } = useGetTaxRatesQuery();

  // Calculate statistics with defensive programming
  const taxRatesArray = Array.isArray(taxRates) ? taxRates : [];
  const activeTaxRates = taxRatesArray.filter(rate => rate.is_active);
  const averageTaxRate = activeTaxRates.length > 0
    ? activeTaxRates.reduce((sum, rate) => sum + Number(rate.rate), 0) / activeTaxRates.length
    : 0;

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Tax Management</h1>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-3 w-full max-w-2xl">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="rates">Tax Rates</TabsTrigger>
          <TabsTrigger value="reports">Tax Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tax Rates</CardTitle>
                <PercentSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <div className="text-2xl font-bold">
                    {isLoadingTaxRates ? (
                      <Loader2 className="h-6 w-6 animate-spin" />
                    ) : (
                      activeTaxRates.length
                    )}
                  </div>
                  {!isLoadingTaxRates && (
                    <Badge variant="secondary">
                      {taxRatesArray.length} total
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Active tax rates configured
                </p>
                {!isLoadingTaxRates && averageTaxRate > 0 && (
                  <p className="text-xs text-muted-foreground">
                    Avg rate: {(averageTaxRate * 100).toFixed(2)}%
                  </p>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => setActiveTab("rates")}
                >
                  View Tax Rates <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tax Reports</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Generate Reports</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Create tax reports for sales, income, and payroll
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => setActiveTab("reports")}
                >
                  View Tax Reports <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tax Calculator</CardTitle>
                <Calculator className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Calculate Taxes</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Quickly calculate taxes for different scenarios
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  asChild
                >
                  <Link href="/taxes/calculator">
                    Open Calculator <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
            <Card>
              <CardHeader>
                <CardTitle>Tax Management Guide</CardTitle>
                <CardDescription>
                  Quick overview of tax management features
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-3">
                  <PercentSquare className="h-5 w-5 mt-0.5 text-primary" />
                  <div>
                    <h3 className="font-medium">Tax Rates</h3>
                    <p className="text-sm text-muted-foreground">
                      Create and manage tax rates that can be applied to invoices, bills, and other transactions.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <FileText className="h-5 w-5 mt-0.5 text-primary" />
                  <div>
                    <h3 className="font-medium">Tax Reports</h3>
                    <p className="text-sm text-muted-foreground">
                      Generate tax reports for different periods to help with tax filing and compliance.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Receipt className="h-5 w-5 mt-0.5 text-primary" />
                  <div>
                    <h3 className="font-medium">Invoice Integration</h3>
                    <p className="text-sm text-muted-foreground">
                      Tax rates are automatically applied to invoices and tracked for reporting.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <BarChart4 className="h-5 w-5 mt-0.5 text-primary" />
                  <div>
                    <h3 className="font-medium">Tax Analytics</h3>
                    <p className="text-sm text-muted-foreground">
                      View tax collection and payment trends over time to better manage cash flow.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common tax management tasks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start" asChild>
                  <Link href="/taxes/rates">
                    <PercentSquare className="mr-2 h-4 w-4" />
                    Manage Tax Rates
                  </Link>
                </Button>

                <Button variant="outline" className="w-full justify-start" asChild>
                  <Link href="/taxes/reports">
                    <FileText className="mr-2 h-4 w-4" />
                    Generate Tax Report
                  </Link>
                </Button>

                <Button variant="outline" className="w-full justify-start" asChild>
                  <Link href="/invoices">
                    <Receipt className="mr-2 h-4 w-4" />
                    Create Invoice with Tax
                  </Link>
                </Button>

                <Button variant="outline" className="w-full justify-start" asChild>
                  <Link href="/dashboard">
                    <BarChart4 className="mr-2 h-4 w-4" />
                    View Tax Dashboard
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rates">
          <Card>
            <CardHeader>
              <CardTitle>Tax Rates</CardTitle>
              <CardDescription>
                Manage tax rates for your business
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-end mb-4">
                <Button asChild>
                  <Link href="/taxes/rates">
                    View All Tax Rates
                  </Link>
                </Button>
              </div>

              {isLoadingTaxRates ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  Loading tax rates...
                </div>
              ) : taxRatesError ? (
                <div className="text-center py-8 text-muted-foreground">
                  Error loading tax rates. Please try again.
                </div>
              ) : taxRatesArray.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {taxRatesArray.slice(0, 4).map((rate) => (
                      <div key={rate.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">{rate.name}</h3>
                            <p className="text-2xl font-bold text-primary">
                              {(Number(rate.rate) * 100).toFixed(2)}%
                            </p>
                            {rate.description && (
                              <p className="text-sm text-muted-foreground mt-1">
                                {rate.description}
                              </p>
                            )}
                          </div>
                          <Badge variant={rate.is_active ? "default" : "secondary"}>
                            {rate.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                  {taxRatesArray.length > 4 && (
                    <div className="text-center">
                      <Button variant="outline" asChild>
                        <Link href="/taxes/rates">
                          View all {taxRatesArray.length} tax rates
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <PercentSquare className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="font-medium mb-2">No tax rates configured</h3>
                  <p className="text-muted-foreground mb-4">
                    Create your first tax rate to get started with tax calculations.
                  </p>
                  <Button asChild>
                    <Link href="/taxes/rates">
                      Create Tax Rate
                    </Link>
                  </Button>
                </div>
              )}

              <div className="mt-6 pt-6 border-t">
                <h3 className="font-medium mb-2">Common Tax Rate Actions:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li>Create new tax rates</li>
                  <li>Edit existing tax rates</li>
                  <li>Delete unused tax rates</li>
                  <li>Apply tax rates to invoices and bills</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports">
          <Card>
            <CardHeader>
              <CardTitle>Tax Reports</CardTitle>
              <CardDescription>
                Generate and view tax reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-end mb-4">
                <Button asChild>
                  <Link href="/taxes/reports">
                    View All Tax Reports
                  </Link>
                </Button>
              </div>
              <p>
                Tax reports help you track and report taxes collected and paid.
                You can generate reports for different tax types and periods to help with tax filing and compliance.
              </p>
              <div className="mt-4">
                <h3 className="font-medium">Available Report Types:</h3>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Sales Tax Reports</li>
                  <li>Income Tax Reports</li>
                  <li>Payroll Tax Reports</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}