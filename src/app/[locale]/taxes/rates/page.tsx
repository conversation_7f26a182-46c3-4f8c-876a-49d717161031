'use client';

import React, { useState } from 'react';
import { 
  useGetTaxRatesQuery, 
  useCreateTaxRateMutation, 
  useUpdateTaxRateMutation, 
  useDeleteTaxRateMutation 
} from '@/redux/services/taxRatesApi';

export default function TaxRatesPage() {
  const { data: taxRates, isLoading, error } = useGetTaxRatesQuery();

  // Ensure taxRates is always an array
  const taxRatesArray = Array.isArray(taxRates) ? taxRates : [];
  const [createTaxRate, { isLoading: isCreating }] = useCreateTaxRateMutation();
  const [updateTaxRate, { isLoading: isUpdating }] = useUpdateTaxRateMutation();
  const [deleteTaxRate, { isLoading: isDeleting }] = useDeleteTaxRateMutation();
  
  const [newTaxRate, setNewTaxRate] = useState({
    name: '',
    rate: 0,
    description: '',
  });
  
  const [editingTaxRate, setEditingTaxRate] = useState<{
    id: string;
    name: string;
    rate: number;
    description: string;
  } | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, isEditing = false) => {
    const { name, value } = e.target;
    if (isEditing && editingTaxRate) {
      setEditingTaxRate({
        ...editingTaxRate,
        [name]: name === 'rate' ? parseFloat(value) : value,
      });
    } else {
      setNewTaxRate({
        ...newTaxRate,
        [name]: name === 'rate' ? parseFloat(value) : value,
      });
    }
  };

  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createTaxRate(newTaxRate).unwrap();
      setNewTaxRate({
        name: '',
        rate: 0,
        description: '',
      });
    } catch (err) {
      console.error('Failed to create tax rate:', err);
    }
  };

  const handleUpdateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingTaxRate) return;
    
    try {
      await updateTaxRate({
        id: editingTaxRate.id,
        body: {
          name: editingTaxRate.name,
          rate: editingTaxRate.rate,
          description: editingTaxRate.description,
        },
      }).unwrap();
      setEditingTaxRate(null);
    } catch (err) {
      console.error('Failed to update tax rate:', err);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this tax rate?')) {
      try {
        await deleteTaxRate(id).unwrap();
      } catch (err) {
        console.error('Failed to delete tax rate:', err);
      }
    }
  };

  if (isLoading) return <div>Loading tax rates...</div>;
  if (error) return <div>Error loading tax rates</div>;

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Tax Rates (RTK Query Example)</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Add New Tax Rate</h2>
        <form onSubmit={handleCreateSubmit} className="space-y-4 max-w-md">
          <div>
            <label className="block mb-1">Name</label>
            <input
              type="text"
              name="name"
              value={newTaxRate.name}
              onChange={(e) => handleInputChange(e)}
              required
              className="w-full p-2 border rounded"
            />
          </div>
          
          <div>
            <label className="block mb-1">Rate (0-1)</label>
            <input
              type="number"
              name="rate"
              value={newTaxRate.rate}
              onChange={(e) => handleInputChange(e)}
              required
              step="0.0001"
              min="0"
              max="1"
              className="w-full p-2 border rounded"
            />
          </div>
          
          <div>
            <label className="block mb-1">Description</label>
            <input
              type="text"
              name="description"
              value={newTaxRate.description}
              onChange={(e) => handleInputChange(e)}
              className="w-full p-2 border rounded"
            />
          </div>
          
          <button
            type="submit"
            disabled={isCreating}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:bg-blue-300"
          >
            {isCreating ? 'Adding...' : 'Add Tax Rate'}
          </button>
        </form>
      </div>
      
      {editingTaxRate && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Edit Tax Rate</h2>
          <form onSubmit={handleUpdateSubmit} className="space-y-4 max-w-md">
            <div>
              <label className="block mb-1">Name</label>
              <input
                type="text"
                name="name"
                value={editingTaxRate.name}
                onChange={(e) => handleInputChange(e, true)}
                required
                className="w-full p-2 border rounded"
              />
            </div>
            
            <div>
              <label className="block mb-1">Rate (0-1)</label>
              <input
                type="number"
                name="rate"
                value={editingTaxRate.rate}
                onChange={(e) => handleInputChange(e, true)}
                required
                step="0.0001"
                min="0"
                max="1"
                className="w-full p-2 border rounded"
              />
            </div>
            
            <div>
              <label className="block mb-1">Description</label>
              <input
                type="text"
                name="description"
                value={editingTaxRate.description}
                onChange={(e) => handleInputChange(e, true)}
                className="w-full p-2 border rounded"
              />
            </div>
            
            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={isUpdating}
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:bg-green-300"
              >
                {isUpdating ? 'Saving...' : 'Save Changes'}
              </button>
              
              <button
                type="button"
                onClick={() => setEditingTaxRate(null)}
                className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}
      
      <div>
        <h2 className="text-xl font-semibold mb-2">Tax Rates</h2>
        {taxRatesArray.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border">
              <thead>
                <tr>
                  <th className="py-2 px-4 border">Name</th>
                  <th className="py-2 px-4 border">Rate</th>
                  <th className="py-2 px-4 border">Description</th>
                  <th className="py-2 px-4 border">Actions</th>
                </tr>
              </thead>
              <tbody>
                {taxRatesArray.map((taxRate) => (
                  <tr key={taxRate.id}>
                    <td className="py-2 px-4 border">{taxRate.name}</td>
                    <td className="py-2 px-4 border">{(taxRate.rate * 100).toFixed(2)}%</td>
                    <td className="py-2 px-4 border">{taxRate.description || '-'}</td>
                    <td className="py-2 px-4 border">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setEditingTaxRate({
                            id: taxRate.id,
                            name: taxRate.name,
                            rate: taxRate.rate,
                            description: taxRate.description || '',
                          })}
                          className="bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 text-sm"
                        >
                          Edit
                        </button>
                        
                        <button
                          onClick={() => handleDelete(taxRate.id)}
                          disabled={isDeleting}
                          className="bg-red-500 text-white px-2 py-1 rounded hover:bg-red-600 text-sm disabled:bg-red-300"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p>No tax rates found.</p>
        )}
      </div>
    </div>
  );
}
