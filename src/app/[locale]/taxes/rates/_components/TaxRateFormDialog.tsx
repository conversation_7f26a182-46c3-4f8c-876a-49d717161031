'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// UI Components
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';

// Types
import { TaxRate } from '@/lib/types';

// API Hooks
import {
  useCreateTaxRateMutation,
  useUpdateTaxRateMutation,
} from '@/redux/services/taxRatesApi';

// Form validation schema
const taxRateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  rate: z.number().min(0, 'Rate must be positive').max(1, 'Rate must be less than or equal to 1'),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
});

type TaxRateFormData = z.infer<typeof taxRateSchema>;

interface TaxRateFormDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  taxRate?: TaxRate | null;
  onSuccess?: () => void;
}

export function TaxRateFormDialog({
  isOpen,
  onOpenChange,
  taxRate,
  onSuccess,
}: TaxRateFormDialogProps) {
  const { toast } = useToast();
  const [error, setError] = useState<string | null>(null);

  // API mutations
  const [createTaxRate, { isLoading: isCreating }] = useCreateTaxRateMutation();
  const [updateTaxRate, { isLoading: isUpdating }] = useUpdateTaxRateMutation();

  const isEditing = !!taxRate?.id;
  const isLoading = isCreating || isUpdating;

  // Form setup
  const form = useForm<TaxRateFormData>({
    resolver: zodResolver(taxRateSchema),
    defaultValues: {
      name: '',
      rate: 0,
      description: '',
      is_active: true,
    },
  });

  // Reset form when taxRate changes or dialog opens
  useEffect(() => {
    if (isOpen) {
      if (taxRate) {
        form.reset({
          name: taxRate.name || '',
          rate: Number(taxRate.rate) || 0,
          description: taxRate.description || '',
          is_active: taxRate.is_active ?? true,
        });
      } else {
        form.reset({
          name: '',
          rate: 0,
          description: '',
          is_active: true,
        });
      }
      setError(null);
    }
  }, [isOpen, taxRate, form]);

  const onSubmit = async (data: TaxRateFormData) => {
    try {
      setError(null);

      if (isEditing && taxRate?.id) {
        // Update existing tax rate
        await updateTaxRate({
          id: taxRate.id,
          body: data,
        }).unwrap();

        toast({
          title: 'Success',
          description: 'Tax rate updated successfully.',
        });
      } else {
        // Create new tax rate
        await createTaxRate(data).unwrap();

        toast({
          title: 'Success',
          description: 'Tax rate created successfully.',
        });
      }

      onSuccess?.();
      onOpenChange(false);
    } catch (err: any) {
      console.error('Error saving tax rate:', err);
      const errorMessage = err?.data?.message || err?.message || 'Failed to save tax rate';
      setError(errorMessage);
      
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  const handleCancel = () => {
    form.reset();
    setError(null);
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Tax Rate' : 'Create New Tax Rate'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the details for this tax rate.' 
              : 'Enter the details for the new tax rate.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              {/* Name Field */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Name <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Sales Tax, VAT"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Rate Field */}
              <FormField
                control={form.control}
                name="rate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Rate <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <div className="flex items-center space-x-2">
                        <Input
                          type="number"
                          step="0.0001"
                          min="0"
                          max="1"
                          placeholder="0.08"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          disabled={isLoading}
                          className="flex-1"
                        />
                        <span className="text-sm text-muted-foreground">
                          ({((field.value || 0) * 100).toFixed(2)}%)
                        </span>
                      </div>
                    </FormControl>
                    <p className="text-xs text-muted-foreground">
                      Enter as decimal (e.g., 0.08 for 8%)
                    </p>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description Field */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Optional description for this tax rate"
                        {...field}
                        disabled={isLoading}
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Active Status */}
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Active</FormLabel>
                      <p className="text-xs text-muted-foreground">
                        Active tax rates can be used in transactions
                      </p>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {error && (
              <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
                {error}
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    {isEditing ? 'Update Tax Rate' : 'Create Tax Rate'}
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
