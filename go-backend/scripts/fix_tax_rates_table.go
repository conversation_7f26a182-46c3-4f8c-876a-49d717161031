package main

import (
	"fmt"
	"log"

	"adc-account-backend/internal/config"
	"adc-account-backend/internal/database"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	fmt.Println("Fixing tax_rates table structure...")

	// Check current table structure
	var result []map[string]interface{}
	if err := db.Raw("SELECT column_name, is_nullable, data_type FROM information_schema.columns WHERE table_name = 'tax_rates' ORDER BY ordinal_position").Scan(&result).Error; err != nil {
		log.Fatalf("Failed to check table structure: %v", err)
	}

	fmt.Println("Current tax_rates table structure:")
	for _, row := range result {
		fmt.Printf("  %s: %s (nullable: %s)\n", row["column_name"], row["data_type"], row["is_nullable"])
	}

	// Drop the merchant_id column if it exists
	var merchantIdExists bool
	err = db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tax_rates' AND column_name = 'merchant_id')").Scan(&merchantIdExists).Error
	if err != nil {
		log.Fatalf("Failed to check merchant_id column: %v", err)
	}

	if merchantIdExists {
		fmt.Println("Dropping merchant_id column...")
		
		// Drop foreign key constraint first
		if err := db.Exec("ALTER TABLE tax_rates DROP CONSTRAINT IF EXISTS fk_tax_rates_merchant").Error; err != nil {
			log.Printf("Warning: Failed to drop merchant foreign key constraint: %v", err)
		}

		// Drop unique constraint
		if err := db.Exec("ALTER TABLE tax_rates DROP CONSTRAINT IF EXISTS uk_tax_rates_merchant_name").Error; err != nil {
			log.Printf("Warning: Failed to drop merchant unique constraint: %v", err)
		}

		// Drop the column
		if err := db.Exec("ALTER TABLE tax_rates DROP COLUMN merchant_id").Error; err != nil {
			log.Fatalf("Failed to drop merchant_id column: %v", err)
		}

		fmt.Println("Successfully dropped merchant_id column")
	} else {
		fmt.Println("merchant_id column does not exist")
	}

	// Ensure branch_id column exists and has proper constraints
	var branchIdExists bool
	err = db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tax_rates' AND column_name = 'branch_id')").Scan(&branchIdExists).Error
	if err != nil {
		log.Fatalf("Failed to check branch_id column: %v", err)
	}

	if !branchIdExists {
		fmt.Println("Adding branch_id column...")
		if err := db.Exec("ALTER TABLE tax_rates ADD COLUMN branch_id VARCHAR(36) NOT NULL").Error; err != nil {
			log.Fatalf("Failed to add branch_id column: %v", err)
		}
	}

	// Add foreign key constraint for branch_id
	if err := db.Exec("ALTER TABLE tax_rates DROP CONSTRAINT IF EXISTS fk_tax_rates_branch").Error; err != nil {
		log.Printf("Warning: Failed to drop existing branch foreign key constraint: %v", err)
	}

	if err := db.Exec("ALTER TABLE tax_rates ADD CONSTRAINT fk_tax_rates_branch FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE").Error; err != nil {
		log.Printf("Warning: Failed to add branch foreign key constraint: %v", err)
	}

	// Add unique constraint for branch_id and name
	if err := db.Exec("ALTER TABLE tax_rates DROP CONSTRAINT IF EXISTS uk_tax_rates_branch_name").Error; err != nil {
		log.Printf("Warning: Failed to drop existing branch unique constraint: %v", err)
	}

	if err := db.Exec("ALTER TABLE tax_rates ADD CONSTRAINT uk_tax_rates_branch_name UNIQUE (branch_id, name)").Error; err != nil {
		log.Printf("Warning: Failed to add branch unique constraint: %v", err)
	}

	// Check final table structure
	if err := db.Raw("SELECT column_name, is_nullable, data_type FROM information_schema.columns WHERE table_name = 'tax_rates' ORDER BY ordinal_position").Scan(&result).Error; err != nil {
		log.Fatalf("Failed to check final table structure: %v", err)
	}

	fmt.Println("Final tax_rates table structure:")
	for _, row := range result {
		fmt.Printf("  %s: %s (nullable: %s)\n", row["column_name"], row["data_type"], row["is_nullable"])
	}

	fmt.Println("Tax rates table structure fixed successfully!")
}
