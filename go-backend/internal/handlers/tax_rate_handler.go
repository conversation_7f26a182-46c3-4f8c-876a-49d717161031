package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

type TaxRateHandler struct {
	taxRateService *services.TaxRateService
}

func NewTaxRateHandler(taxRateService *services.TaxRateService) *TaxRateHandler {
	return &TaxRateHandler{
		taxRateService: taxRateService,
	}
}

// GetTaxRates godoc
// @Summary Get all tax rates
// @Description Get all tax rates with pagination
// @Tags tax-rates
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} PaginatedResponse{data=[]services.TaxRateResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /tax-rates [get]
func (h *TaxRateHandler) GetTaxRates(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	taxRates, total, err := h.taxRateService.GetAllTaxRates(page, limit, userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       taxRates,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// GetTaxRatesByMerchant godoc
// @Summary Get tax rates by merchant
// @Description Get tax rates for a specific merchant with pagination
// @Tags tax-rates
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} PaginatedResponse{data=[]services.TaxRateResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/tax-rates [get]
func (h *TaxRateHandler) GetTaxRatesByMerchant(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	merchantID := c.Param("merchantId")
	if merchantID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Merchant ID is required"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	taxRates, total, err := h.taxRateService.GetTaxRatesByBranch(merchantID, page, limit, userID.(string))
	if err != nil {
		if err.Error() == "access denied to this branch" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       taxRates,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// GetTaxRate godoc
// @Summary Get tax rate by ID
// @Description Get a specific tax rate by ID
// @Tags tax-rates
// @Accept json
// @Produce json
// @Param id path string true "Tax Rate ID"
// @Success 200 {object} services.TaxRateResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /tax-rates/{id} [get]
func (h *TaxRateHandler) GetTaxRate(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Tax Rate ID is required"})
		return
	}

	taxRate, err := h.taxRateService.GetTaxRateByID(id, userID.(string))
	if err != nil {
		if err.Error() == "tax rate not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, taxRate)
}

// CreateTaxRate godoc
// @Summary Create a new tax rate
// @Description Create a new tax rate
// @Tags tax-rates
// @Accept json
// @Produce json
// @Param taxRate body services.CreateTaxRateRequest true "Tax Rate data"
// @Success 201 {object} services.TaxRateResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /tax-rates [post]
func (h *TaxRateHandler) CreateTaxRate(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	var req services.CreateTaxRateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	taxRate, err := h.taxRateService.CreateTaxRate(req, userID.(string))
	if err != nil {
		if err.Error() == "access denied to this branch" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, taxRate)
}

// UpdateTaxRate godoc
// @Summary Update a tax rate
// @Description Update an existing tax rate
// @Tags tax-rates
// @Accept json
// @Produce json
// @Param id path string true "Tax Rate ID"
// @Param taxRate body services.UpdateTaxRateRequest true "Tax Rate data"
// @Success 200 {object} services.TaxRateResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /tax-rates/{id} [put]
func (h *TaxRateHandler) UpdateTaxRate(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Tax Rate ID is required"})
		return
	}

	var req services.UpdateTaxRateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	taxRate, err := h.taxRateService.UpdateTaxRate(id, req, userID.(string))
	if err != nil {
		if err.Error() == "tax rate not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, taxRate)
}

// DeleteTaxRate godoc
// @Summary Delete a tax rate
// @Description Soft delete a tax rate
// @Tags tax-rates
// @Accept json
// @Produce json
// @Param id path string true "Tax Rate ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /tax-rates/{id} [delete]
func (h *TaxRateHandler) DeleteTaxRate(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Tax Rate ID is required"})
		return
	}

	err := h.taxRateService.DeleteTaxRate(id, userID.(string))
	if err != nil {
		if err.Error() == "tax rate not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetDefaultTaxRate godoc
// @Summary Get default tax rate for merchant
// @Description Get the default tax rate for a specific merchant
// @Tags tax-rates
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Success 200 {object} services.TaxRateResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/tax-rates/default [get]
func (h *TaxRateHandler) GetDefaultTaxRate(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	merchantID := c.Param("merchantId")
	if merchantID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Merchant ID is required"})
		return
	}

	taxRate, err := h.taxRateService.GetDefaultTaxRate(merchantID, userID.(string))
	if err != nil {
		if err.Error() == "access denied to this branch" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		if err.Error() == "no default tax rate found for this branch" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, taxRate)
}

// CalculateTax godoc
// @Summary Calculate tax amount
// @Description Calculate tax amount based on tax rate and base amount
// @Tags tax-rates
// @Accept json
// @Produce json
// @Param taxRateId path string true "Tax Rate ID"
// @Param amount query string true "Base amount"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /tax-rates/{taxRateId}/calculate [get]
func (h *TaxRateHandler) CalculateTax(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	taxRateID := c.Param("taxRateId")
	if taxRateID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Tax Rate ID is required"})
		return
	}

	amountStr := c.Query("amount")
	if amountStr == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Amount is required"})
		return
	}

	baseAmount, err := decimal.NewFromString(amountStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid amount format"})
		return
	}

	taxAmount, err := h.taxRateService.CalculateTax(taxRateID, baseAmount, userID.(string))
	if err != nil {
		if err.Error() == "tax rate not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"baseAmount":  baseAmount,
		"taxAmount":   taxAmount,
		"totalAmount": baseAmount.Add(taxAmount),
	})
}

// GetAllTaxRates is an alias for GetTaxRates for route compatibility
func (h *TaxRateHandler) GetAllTaxRates(c *gin.Context) {
	h.GetTaxRates(c)
}

// GetTaxRateByID is an alias for GetTaxRate for route compatibility
func (h *TaxRateHandler) GetTaxRateByID(c *gin.Context) {
	h.GetTaxRate(c)
}
